import { createApp, type Component } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import Antd from 'ant-design-vue'
import { message, Modal, notification } from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'
import router from './router/'
import { parseTime } from '@/utils/parseTime'

// Directive
import permission from '@/directive/permission/permission'

// 引入 Ant Design 图标库
import * as AntdIcons from '@ant-design/icons-vue'

console.log(import.meta.env)

// Initialize the Pinia instance
const pinia = createPinia()
const app = createApp(App)

app.directive('has', permission.checkPermission)

// 挂载全局变量
app.config.globalProperties.message = message
app.config.globalProperties.modal = Modal
app.config.globalProperties.notification = notification
app.config.globalProperties.parseTime = parseTime

// 挂载全局图标
for (const name in AntdIcons) {
  app.component(name, (AntdIcons as Record<string, Component>)[name])
}

app.use(Antd)
app.use(router)
app.use(pinia)
app.mount('#app')
