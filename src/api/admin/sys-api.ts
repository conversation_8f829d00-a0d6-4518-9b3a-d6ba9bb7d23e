import request from '../../utils/request'

const url = '/api/v1/sys-api'

interface ApiParams {
  [key: string]: unknown
}

interface ApiData {
  [key: string]: unknown
}

export function getSysApi(params: ApiParams) {
  return request({
    url,
    method: 'get',
    params,
  })
}

export function addSys<PERSON>pi(data: ApiData) {
  return request({
    url,
    method: 'post',
    data,
  })
}

export function removeSysApi(data: ApiData) {
  return request({
    url,
    method: 'delete',
    data,
  })
}

export function updateSysApi(data: ApiData, id: string | number) {
  return request({
    url: `${url}/${id}`,
    method: 'put',
    data,
  })
}
