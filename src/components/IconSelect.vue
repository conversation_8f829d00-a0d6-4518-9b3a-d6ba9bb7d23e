<template>
  <a-select
    v-model="dataModel"
    :options="IconSelect"
    :trigger-props="{ contentClass: 'iconSelect' }"
    allow-search
    allow-clear
    placeholder="请选择菜单图标"
  >
    <template #label="{ data }">
      <a-space>
        <component :is="data.value" :width="size" :height="size" :size="size.toString()" />
        <span v-if="title">{{ data?.value }}</span>
      </a-space>
    </template>
    <template #option="{ data }">
      <component :is="data.value" :width="size" :height="size" :size="size.toString()" />
    </template>
  </a-select>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import * as ArcoIconModules from '@arco-design/web-vue/es/icon/index.js'
import * as RemixIcon from '@remixicon/vue'

interface Props {
  data?: string
  title?: boolean
  size?: number | string
}

interface Emits {
  'update:data': [value: string]
  Change: [value: boolean]
}

const props = withDefaults(defineProps<Props>(), {
  data: '',
  title: true,
  size: 16,
})

const emits = defineEmits<Emits>()

const dataModel = computed({
  get(): string {
    return props.data || ''
  },
  set(val: string): void {
    emits('update:data', val)
  },
})

const IconSelect = computed(() => {
  const iconList: string[] = []

  for (const key in RemixIcon) {
    const icon = (RemixIcon as unknown as Record<string, { name?: string }>)[key]
    if (icon?.name) {
      iconList.push(icon.name)
    }
  }

  for (const key in ArcoIconModules) {
    const icon = (ArcoIconModules as unknown as Record<string, { name?: string }>)[key]
    if (icon?.name) {
      iconList.push(icon.name)
    }
  }
  return iconList
})

// const handleClose = (): void => {
//   emits('Change', false)
// }
</script>

<style lang="scss">
.iconSelect .arco-select-dropdown-list {
  display: flex;
  flex-wrap: wrap;
}

.iconSelect .arco-select-option {
  width: auto;
}
</style>
