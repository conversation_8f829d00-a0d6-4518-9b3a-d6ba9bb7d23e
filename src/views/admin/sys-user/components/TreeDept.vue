<template>
  <div class="tree-container">
    <a-tree
      :data="props.data"
      :field-names="{ key: 'deptId', title: 'deptName' }"
      block-node
      @select="handleTreeSelect"
    />
  </div>
</template>

<script setup lang="ts">
interface TreeNode {
  id: string | number
  label: string
  children?: TreeNode[]
  [key: string]: unknown
}

interface Props {
  data: TreeNode[]
}

const props = withDefaults(defineProps<Props>(), {
  data: () => [],
})

const emits = defineEmits(['nodeClick'])

const handleTreeSelect = (selectKeys: (string | number)[]): void => {
  emits('nodeClick', { deptId: `/${selectKeys}/` })
}
</script>

<style lang="scss">
.tree-container {
  padding-right: 20px;
}
</style>
