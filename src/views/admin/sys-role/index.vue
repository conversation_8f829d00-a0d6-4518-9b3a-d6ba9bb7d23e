<template>
  <div class="container">
    <a-card :bordered="false" class="cardStyle" style="margin-bottom: 16px">
      <a-list-item-meta>
        <template #title>
          <div class="akaInfoTitle">角色管理</div>
        </template>
        <template #description>
          <div class="akaInfoDesc">在这里管理用户所绑定的角色关系</div>
        </template>
        <template #avatar>
          <div style="border-radius: 100px 0 100px 100px; background-color: #eff4f9; padding: 6px">
            <Iconify
              icon="eos-icons:role-binding-outlined"
              style="color: black"
              width="48"
              height="48"
            />
          </div>
        </template>
      </a-list-item-meta>
      <a-divider />
      <a-card-meta>
        <template #avatar>
          <a-form :model="queryForm" ref="queryFormRef" layout="inline">
            <a-form-item name="roleName" label="角色名称">
              <a-input
                v-model:value="queryForm.roleName"
                placeholder="请输入角色名称"
                @press-enter="handleQuery"
              />
            </a-form-item>
            <a-form-item name="roleKey" label="权限字符">
              <a-input
                v-model:value="queryForm.roleKey"
                placeholder="请输入权限字符"
                @press-enter="handleQuery"
              />
            </a-form-item>
            <a-form-item name="status" label="状态">
              <a-select v-model:value="queryForm.status" placeholder="请选择角色状态">
                <a-option :value="2">正常</a-option>
                <a-option :value="1">停用</a-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="handleQuery"><SearchOutlined /> 搜索</a-button>
                <a-button @click="handleResetQuery"><ReloadOutlined /> 重置</a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </template>
      </a-card-meta>
      <template #actions>
        <a-space class="action">
          <a-button v-has="'admin:sysRole:add'" type="primary" @click="handleAdd"
            ><PlusOutlined /> 新增</a-button
          >
          <a-button
            v-has="'admin:sysRole:remove'"
            type="primary"
            danger
            @click="
              () => {
                deleteVisible = true
              }
            "
            ><DeleteOutlined /> 批量删除</a-button
          >
          <a-button type="primary" danger disabled><DownloadOutlined /> 导出</a-button>
        </a-space>
      </template>
    </a-card>

    <a-card :bordered="false" class="cardStyle">
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="{
          showTotal: true,
          showSizeChanger: true,
          showQuickJumper: true,
          total: pager.count,
          current: currentPage,
          pageSize: pager.pageSize,
        }"
        row-key="roleId"
        :row-selection="{ type: 'checkbox', preserveSelectedRowKeys: true }"
        @change="handleTableChange"
        @select="handleSelect"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'status'">
            <a-tag v-if="record.status == 2" color="success">正常</a-tag>
            <a-tag v-else color="error">停用</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'createdAt'">
            {{ parseTime(record.createdAt) }}
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a-button v-has="'admin:sysRole:edit'" type="link" @click="handleUpdate(record)"
              ><EditOutlined /> 修改</a-button
            >
            <a-button v-has="'admin:sysRole:edit'" type="link" @click="handleDataScope(record)"
              ><ControlOutlined /> 数据权限</a-button
            >
            <a-button
              v-has="'admin:sysRole:remove'"
              type="link"
              danger
              @click="
                () => {
                  deleteVisible = true
                  deleteData = [record.roleId]
                }
              "
              ><DeleteOutlined /> 删除</a-button
            >
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/修改角色 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="title"
      @ok="handleBeforeOk"
      @cancel="handleCancel"
    >
      <a-form
        :model="modalForm"
        :rules="rules"
        ref="modalFormRef"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
      >
        <a-form-item name="roleName" label="角色名称">
          <a-input v-model:value="modalForm.roleName" placeholder="请输入角色名称" />
        </a-form-item>
        <a-form-item name="roleKey" label="权限字符">
          <a-input v-model:value="modalForm.roleKey" placeholder="请输入权限字符" />
        </a-form-item>
        <a-form-item name="sort" label="排序">
          <a-input-number
            v-model:value="modalForm.sort"
            placeholder="请输入排序"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item name="status" label="状态">
          <a-radio-group v-model:value="modalForm.status">
            <a-radio value="2">正常</a-radio>
            <a-radio value="1">停用</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="权限设置">
          <a-tree
            v-model:checked-keys="checkedKeys"
            :checkable="true"
            :check-strictly="false"
            :tree-data="treeData"
            :default-expand-all="false"
            :field-names="{ key: 'id', title: 'label' }"
          />
        </a-form-item>
        <a-form-item name="remark" label="备注">
          <a-textarea v-model:value="modalForm.remark" placeholder="请输入备注内容" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- DataScope Modal -->
    <a-modal
      v-model:visible="scopedModalVisible"
      :title="title"
      @ok="handleScopeBeforeOk"
      @cancel="handleCancel"
    >
      <a-form :model="scopeForm" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <a-form-item name="roleName" label="角色名称">
          <a-input v-model:value="scopeForm.roleName" disabled />
        </a-form-item>
        <a-form-item name="roleKey" label="权限字符">
          <a-input v-model:value="scopeForm.roleKey" disabled />
        </a-form-item>
        <a-form-item name="dataScope" label="权限范围">
          <a-select v-model:value="scopeForm.dataScope" placeholder="请选择权限范围">
            <a-option
              v-for="item in dataScopeOptions"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            />
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- Akiraka 20230223 删除与批量删除 开始 -->
    <DeleteModal
      :data="deleteData"
      :visible="deleteVisible"
      :apiDelete="removeRole"
      @deleteVisibleChange="() => (deleteVisible = false)"
    />
    <!-- Akiraka 20230223 删除与批量删除 结束 -->
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, nextTick, watch } from 'vue'
import {
  getRole,
  addRole,
  updateRole,
  removeRole,
  updateRoleScoped,
  getRoleMenuTree,
} from '@/api/admin/role'
import { message, notification } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
  DeleteOutlined,
  DownloadOutlined,
  EditOutlined,
  ControlOutlined,
} from '@ant-design/icons-vue'
import type { FormInstance, TablePaginationConfig } from 'ant-design-vue'
import { parseTime } from '@/utils/parseTime'

// 表单引用
const queryFormRef = ref<FormInstance>()
const modalFormRef = ref<FormInstance>()

// Akiraka 20230210 删除数据
const deleteData = ref([])
// Akiraka 20230210 删除对话框
const deleteVisible = ref(false)
// Akiraka 20230210 监听删除事件
watch(
  () => deleteVisible.value,
  (value) => {
    if (value === false) {
      getRoleInfo({ ...pager, ...queryForm })
    }
  },
)

const currentPage = ref(1)
// Pager
const pager = {
  count: 0,
  pageIndex: 1,
  pageSize: 10,
}

// Batch Delete List
const batchDeleteList = ref([])

// Form
const queryForm = reactive({})
const modalForm = reactive({
  sort: 0,
  status: '2',
})
const scopeForm = reactive({})

// rules
const rules = {
  roleName: [{ required: true, message: '请输入角色名称' }],
  roleKey: [{ required: true, message: '请输入权限字符' }],
}

// ScopeOption
const dataScopeOptions = [
  {
    value: '1',
    label: '全部数据权限',
  },
  {
    value: '2',
    label: '自定数据权限',
  },
  {
    value: '3',
    label: '本部门数据权限',
  },
  {
    value: '4',
    label: '本部门及以下数据权限',
  },
  {
    value: '5',
    label: '仅本人数据权限',
  },
]

// Table Columns
const columns = [
  { title: '编号', dataIndex: 'roleId' },
  { title: '角色名称', dataIndex: 'roleName' },
  { title: '权限字符', dataIndex: 'roleKey' },
  { title: '排序', dataIndex: 'roleSort' },
  { title: '状态', dataIndex: 'status' },
  { title: '创建时间', dataIndex: 'createdAt' },
  { title: '操作', dataIndex: 'action', width: 250 },
]

// Table Data;
const tableData = ref([])

// Tree Data;
const checkedKeys = ref([])
const treeData = ref([])

// Modal
const modalVisible = ref(false)
const scopedModalVisible = ref(false)
const title = ref('默认标题')

// Table Select
const handleSelect = (rowKey) => {
  batchDeleteList.value = rowKey
}

// 表格分页、排序、筛选变化
const handleTableChange = (pagination: TablePaginationConfig) => {
  if (pagination.current !== currentPage.value) {
    handlePageChange(pagination.current || 1)
  }
  if (pagination.pageSize !== pager.pageSize) {
    handlePageSizeChange(pagination.pageSize || 10)
  }
}

// 查询
const handleQuery = async () => {
  const res = await getRole({ ...pager, ...queryForm })
  const { count, list, pageIndex, pageSize } = res.data

  tableData.value = list
  Object.assign(pager, { count, pageIndex, pageSize })
}

// 重置查询
const handleResetQuery = () => {
  queryFormRef.value?.resetFields()
  getRoleInfo(queryForm)
}

// 创建
const handleAdd = () => {
  modalVisible.value = true
  title.value = '创建角色'
}

// 修改角色
const handleUpdate = async (record) => {
  modalVisible.value = true
  title.value = '修改角色'

  await nextTick()
  Object.assign(modalForm, record)

  // 显示勾选的菜单，checkedKeys 传入id数组即可
  const menuIdsChecked = []
  record.sysMenu.forEach((item) => {
    menuIdsChecked.push(item.menuId)
  })
  checkedKeys.value = menuIdsChecked
}

// 分配数据权限
const handleDataScope = async (record) => {
  scopedModalVisible.value = true
  title.value = '分配数据权限'

  const { roleKey, roleName, dataScope, roleId } = record
  await nextTick()
  Object.assign(scopeForm, { roleKey, roleName, dataScope, roleId })
}

/**
 * 分页改变
 * @param {Number} [page]
 */
const handlePageChange = (page) => {
  pager.pageIndex = page

  // 修改当前页码
  currentPage.value = page
  getRoleInfo({ ...pager, ...queryForm })
}

// 每页数据量
const handlePageSizeChange = (pageSize) => {
  pager.pageSize = pageSize
  getRoleInfo({ ...pager, ...queryForm })
}

// 角色管理表单提交
// 异步关闭表单需要使用 done() 回调函数
const handleBeforeOk = () => {
  modalFormRef.value
    ?.validate()
    .then(async () => {
      modalForm.menuIds = checkedKeys.value
      if (modalForm.roleId) {
        const { code, msg } = await updateRole(modalForm, modalForm.roleId)
        if (code === 200) {
          notification.success({
            message: '更新成功',
            description: '角色信息已更新',
          })
        } else {
          notification.error({
            message: '更新失败',
            description: msg,
          })
        }
      } else {
        const { code, msg } = await addRole(modalForm)
        if (code === 200) {
          notification.success({
            message: '新增成功',
            description: '角色已创建',
          })
        } else {
          notification.error({
            message: '新增失败',
            description: msg,
          })
        }
      }
      getRoleInfo()
    })
    .catch(() => {
      message.error('数据校验失败')
    })
}

// 数据权限表单提交
const handleScopeBeforeOk = async () => {
  const res = await updateRoleScoped(scopeForm)
  message.success(res.msg)
  getRoleInfo()
}

// 重置数据表单
const handleCancel = () => {
  modalVisible.value = false
  modalForm.roleId = null
  scopeForm.roleId = null
  checkedKeys.value = []
  modalFormRef.value?.resetFields()
}

// 获取角色信息
const getRoleInfo = async (params = {}) => {
  const { data, code, msg } = await getRole(params)
  if (code === 200) {
    tableData.value = data.list
    Object.assign(pager, { count: data.count, pageIndex: data.pageIndex, pageSize: data.pageSize })
  } else {
    notification.error({
      message: '获取数据失败',
      description: msg,
    })
  }
}

// 获取角色菜单信息
const getRoleMenuTreeInfo = async () => {
  const res = await getRoleMenuTree({}, 0)
  treeData.value = res.data.menus
  checkedKeys.value = res.data.checkedKeys
}

onMounted(() => {
  getRoleInfo()
  getRoleMenuTreeInfo()
})
</script>

<style setup>
.action {
  margin-bottom: 12px;
}
</style>
