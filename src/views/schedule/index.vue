<template>
  <div class="container">
    <a-card :bordered="false" class="cardStyle" style="margin-bottom: 16px">
      <a-list-item-meta>
        <template #title>
          <div class="akaInfoTitle">定时任务</div>
        </template>
        <template #description>
          <div class="akaInfoDesc">定时执行任务满足需求</div>
        </template>
        <template #avatar>
          <div style="border-radius: 100px 0 100px 100px; background-color: #eff4f9; padding: 6px">
            <Iconify icon="eos-icons:cronjob" style="color: black" width="48" height="48" />
          </div>
        </template>
      </a-list-item-meta>
      <a-divider />
      <a-card-meta>
        <template #avatar>
          <a-form :model="queryForm" ref="queryFormRef" layout="inline">
            <a-form-item name="jobName" label="任务名称">
              <a-input
                v-model:value="queryForm.jobName"
                placeholder="请输入任务名称"
                @press-enter="handleQuery"
              />
            </a-form-item>
            <a-form-item name="jobGroup" label="任务分组">
              <a-select v-model:value="queryForm.jobGroup" placeholder="请选择任务分组">
                <a-option value="DEFAULT">默认</a-option>
                <a-option value="SYSTEM">系统</a-option>
              </a-select>
            </a-form-item>
            <a-form-item name="status" label="状态">
              <a-select v-model:value="queryForm.status" placeholder="请选择任务状态">
                <a-option :value="2">正常</a-option>
                <a-option :value="1">关闭</a-option>
              </a-select>
            </a-form-item>
            <a-form-item>
              <a-space>
                <a-button type="primary" @click="handleQuery">查询</a-button>
                <a-button @click="handleResetQuery">重置</a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </template>
      </a-card-meta>
      <template #actions>
        <a-space class="action">
          <a-button type="primary" @click="handleAdd">新增定时任务</a-button>
        </a-space>
      </template>
    </a-card>

    <a-card :bordered="false" class="cardStyle">
      <a-table
        :data-source="tableData"
        :columns="columns"
        :pagination="{
          'show-total': true,
          'show-jumper': true,
          'show-page-size': true,
          total: pager.count,
          current: currentPage,
        }"
      >
        <template #status="{ record }">
          <a-tag v-if="record.status == 2" color="green">正常</a-tag>
          <a-tag v-if="record.status == 1" color="red">停用</a-tag>
        </template>
        <template #action="{ record }">
          <a-button type="text" @click="handleUpdate(record)">修改</a-button>
          <a-button
            type="text"
            status="success"
            v-if="record.entry_id == 0"
            @click="handleStart(record.jobId)"
            >启动</a-button
          >
          <a-button
            type="text"
            status="danger"
            v-if="record.entry_id !== 0"
            @click="handleStop(record.jobId)"
            >停止</a-button
          >
          <a-button
            type="text"
            status="danger"
            @click="
              () => {
                deleteVisible = true
                deleteData = [record.jobId]
              }
            "
            >删除</a-button
          >
        </template>
      </a-table>
    </a-card>

    <a-modal
      v-model:visible="modalVisible"
      :title="modalTitle"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <a-form
        :model="modalForm"
        :rules="rules"
        ref="modalFormRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item name="jobName" label="任务名称">
          <a-input v-model:value="modalForm.jobName" placeholder="请输入任务名称"></a-input>
        </a-form-item>
        <a-form-item name="jobGroup" label="任务分组">
          <a-select v-model:value="modalForm.jobGroup" placeholder="请选择任务分组">
            <a-option value="DEFAULT">默认</a-option>
            <a-option value="SYSTEM">系统</a-option>
          </a-select>
        </a-form-item>
        <a-form-item name="invokeTarget" label="调用目标">
          <a-input v-model:value="modalForm.invokeTarget" placeholder="调用目标"></a-input>
        </a-form-item>
        <a-form-item name="args" label="目标参数">
          <a-input v-model:value="modalForm.args" placeholder="目标参数"></a-input>
        </a-form-item>
        <a-form-item name="cronExpression" label="Cron表达式">
          <a-input v-model:value="modalForm.cronExpression" placeholder="Cron表达式"></a-input>
        </a-form-item>
        <a-form-item name="concurrent" label="是否并发">
          <a-radio-group v-model:value="modalForm.concurrent">
            <a-radio :value="0">允许</a-radio>
            <a-radio :value="1">禁止</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item name="jobType" label="调用类型">
          <a-radio-group v-model:value="modalForm.jobType">
            <a-radio :value="1">接口</a-radio>
            <a-radio :value="2">函数</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item name="misfirePolicy" label="执行策略">
          <a-radio-group v-model:value="modalForm.misfirePolicy">
            <a-radio :value="1">立即执行</a-radio>
            <a-radio :value="2">执行一次</a-radio>
            <a-radio :value="3">放弃执行</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item name="status" label="状态">
          <a-select v-model:value="modalForm.status">
            <a-option :value="2">正常</a-option>
            <a-option :value="1">停用</a-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- Akiraka 20230223 删除与批量删除 开始 -->
    <DeleteModal
      :data="deleteData"
      :visible="deleteVisible"
      :apiDelete="delSysJob"
      @deleteVisibleChange="() => (deleteVisible = false)"
    />
    <!-- Akiraka 20230223 删除与批量删除 结束 -->
  </div>
</template>

<script setup lang="ts">
import { listSysJob, addSysJob, updateSysJob, delSysJob, startJob, removeJob } from '@/api/sys-job'
import type { FormInstance } from 'ant-design-vue'

interface QueryForm {
  jobName?: string
  jobGroup?: string
  status?: number
  [key: string]: unknown
}

interface ModalForm {
  jobId?: string | number
  jobName: string
  jobGroup: string
  invokeTarget: string
  args: string
  cronExpression: string
  concurrent: number
  jobType: number
  misfirePolicy: number
  status: number
  [key: string]: unknown
}

interface JobRecord {
  jobId: string | number
  jobName: string
  jobGroup: string
  cronExpression: string
  invokeTarget: string
  status: number
  entry_id: number
  [key: string]: unknown
}

interface Pager {
  count: number
  pageIndex: number
  pageSize: number
}

// Akiraka 20230210 删除数据
const deleteData = ref<(string | number)[]>([])
// Akiraka 20230210 删除对话框
const deleteVisible = ref<boolean>(false)
// Akiraka 20230210 监听删除事件
watch(
  () => deleteVisible.value,
  (value) => {
    if (value === false) {
      getSysJobListInfo(queryForm)
    }
  },
)

// 表单引用
const queryFormRef = ref<FormInstance>()
const modalFormRef = ref<FormInstance>()

const currentPage = ref<number>(1)
// Pager
const pager = reactive<Pager>({
  count: 0,
  pageIndex: 1,
  pageSize: 10,
})

const queryForm = reactive<QueryForm>({})
const modalForm = reactive<ModalForm>({
  jobName: '',
  jobGroup: '',
  invokeTarget: '',
  args: '',
  cronExpression: '',
  concurrent: 1,
  jobType: 1,
  misfirePolicy: 1,
  status: 2,
})

// 表单检验规则
const rules = {
  jobName: [{ required: true, message: '请输入任务名称' }],
  jobGroup: [{ required: true, message: '请选择任务分组' }],
  invokeTarget: [{ required: true, message: '请输入调用目标' }],
  cronExpression: [{ required: true, message: '请输入Cron表达式' }],
  status: [{ required: true, message: '请选择状态' }],
}

const modalVisible = ref<boolean>(false)
const modalTitle = ref<string>('默认标题')

const tableData = ref<JobRecord[]>([])
const columns = [
  { title: '编号', dataIndex: 'jobId' },
  { title: '任务名称', dataIndex: 'jobName' },
  { title: '任务分组', dataIndex: 'jobGroup' },
  { title: '任务表达式', dataIndex: 'cronExpression' },
  { title: '调用目标', dataIndex: 'invokeTarget' },
  { title: '状态', dataIndex: 'status', slotName: 'status' },
  { title: '操作', slotName: 'action' },
]

// 查询任务
const handleQuery = (): void => {
  getSysJobListInfo(queryForm)
}

// 重置查询
const handleResetQuery = (): void => {
  queryFormRef.value?.resetFields()
  getSysJobListInfo()
}

// 新增任务
const handleAdd = (): void => {
  modalVisible.value = true
  modalTitle.value = '新增任务'
}

// 修改任务
const handleUpdate = (record: JobRecord): void => {
  modalVisible.value = true
  modalTitle.value = '修改任务'

  Object.assign(modalForm, record)
}

// 取消操作
const handleCancel = (): void => {
  modalFormRef.value?.resetFields()
}

// 启动定时任务
const handleStart = async (jobId: string | number): Promise<void> => {
  await startJob(jobId)
  notification.success({
    message: '启动任务成功！',
    description: '任务已成功启动',
  })
  getSysJobListInfo()
}

// 关闭定时任务
const handleStop = async (jobId: string | number): Promise<void> => {
  await removeJob(jobId)
  notification.success({
    message: '已停止任务！',
    description: '任务已成功停止',
  })
  getSysJobListInfo()
}

// Modal 触发oK事件前
const onBeforeOk = (done: () => void): void => {
  modalFormRef.value
    ?.validate()
    .then(() => {
      return done()
    })
    .catch(() => {
      message.error('表单校验失败')
      return done(false)
    })
}

// Modal 触发ok事件
const handleOk = async (): Promise<void> => {
  if (modalForm.jobId) {
    const { code, msg } = await updateSysJob(modalForm)
    if (code === 200) {
      notification.success({
        message: '修改成功',
        description: '任务信息已更新',
      })
    } else {
      notification.error({
        message: '修改失败',
        description: msg,
      })
    }
  } else {
    const { code, msg } = await addSysJob(modalForm)
    if (code === 200) {
      notification.success({
        message: '新增成功',
        description: '任务已创建',
      })
    } else {
      notification.error({
        message: '新增失败',
        description: msg,
      })
    }
  }
  getSysJobListInfo()
}

// 获取系统任务信息
const getSysJobListInfo = async (params: QueryForm = {}): Promise<void> => {
  const { data, code, msg } = await listSysJob(params)
  if (code === 200) {
    tableData.value = data.list
    Object.assign(pager, { count: data.count, pageIndex: data.pageIndex, pageSize: data.pageSize })
  } else {
    notification.error({
      message: '获取数据失败',
      description: msg,
    })
  }
}

onMounted(() => {
  getSysJobListInfo()
})
</script>

<style lang="scss" scoped>
.table-action {
  margin-bottom: 12px;
}
</style>
