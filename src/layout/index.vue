<template>
  <a-layout :style="{ height: '100vh' }">
    <a-layout-sider :trigger="null" :width="220" :collapsed="collapsed" collapsible>
      <Menu :collapsed="collapsed" />
    </a-layout-sider>
    <a-layout>
      <a-layout-header>
        <Navbar :collapsed="collapsed" @on-collapse="onCollapse" />
        <tag-view />
      </a-layout-header>
      <a-layout-content class="layout-content">
        <AppMain />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { AppMain, Navbar } from './components'
import Menu from './components/Menu/Menu.vue'
import TagView from './components/TagView/TagView.vue'

const collapsed = ref<boolean>(false)

const onCollapse = (): void => {
  collapsed.value = !collapsed.value
}
</script>

<style lang="scss">
@import '../style/index.scss';
@import '../style/transition.scss';
@import '../style/dark-theme.scss';

.layout-content {
  padding: 16px;
  background-color: #ffffff;
}
</style>
