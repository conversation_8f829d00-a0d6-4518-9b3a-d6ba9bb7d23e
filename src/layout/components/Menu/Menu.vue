<template>
  <div class="sider-logo">
    <img :src="store.sysConfig?.sys_app_logo" />
    <span class="sider-title" v-if="!props.collapsed">{{ store.sysConfig?.sys_app_name }}</span>
  </div>
  <a-menu
    class="menu"
    @click="handleMenuClick"
    :default-open-keys="['/admin']"
    :selected-keys="defaultSelectKeys"
    mode="inline"
    theme="dark"
  >
    <sub-menu :menu-list="permissionStore.menuList" />
  </a-menu>
</template>

<script setup lang="ts">
import { useUserStore } from '@/store/userInfo'
import { usePermissionStore } from '@/store/permission'

import SubMenu from './SubMenu.vue'

interface Props {
  collapsed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  collapsed: false,
})

const store = useUserStore()
const permissionStore = usePermissionStore()

const route = useRoute()
const router = useRouter()

// 默认菜单选中
const defaultSelectKeys = ref<string[]>([])

// 刷新保持菜单选中
const keepDefaultSelect = (): void => {
  defaultSelectKeys.value = []
  defaultSelectKeys.value.push(route.fullPath)
}

// 修复菜单点击事件处理
const handleMenuClick = (menuInfo: { key: string }): void => {
  console.log('Menu clicked:', menuInfo.key)
  router.push(menuInfo.key)
}

// 监听路由变化，更新选中菜单
watch(
  () => route.fullPath,
  (newPath) => {
    defaultSelectKeys.value = [newPath]
  },
)

onBeforeMount(() => {
  keepDefaultSelect()
})
</script>

<style lang="scss" scoped>
.sider-logo {
  margin: 10px 0;
  display: flex;
  justify-content: center;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.17);
  & img {
    height: 32px;
  }
}

.sider-title {
  margin-left: 10px;
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 700;
  color: #fff;
}

.left-side {
  height: 50px;
  width: 50px;
  font-size: 18px;
  line-height: 50px;
  text-align: center;
  transition: all 0.3s ease-in-out;
  cursor: pointer;

  &:hover {
    background-color: #e5e5e5;
  }
}
</style>

<style lang="scss">
/* 覆盖 Ant Design Vue 菜单样式 */
.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff;
}

/* 移除 Arco Design 特有的样式 */
.arco-menu-indent {
  width: 30px;
}
</style>
