<script setup lang="ts">
import { useTagViewStore } from '@/store/tagView'
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue'
import type { RouteLocationNormalized } from 'vue-router'

interface TagView extends Partial<RouteLocationNormalized> {
  title?: string
  meta?: {
    title?: string
    [key: string]: unknown
  }
}

const store = useTagViewStore()
const route = useRoute()
const router = useRouter()

const tagListRef = ref<HTMLElement | null>(null)
const tagMore = ref<boolean>(false)

const checkTagListWidth = async (): Promise<void> => {
  await nextTick()
  if (!tagListRef.value) return

  // 获取 tagList 宽度
  const tagListWidth = tagListRef.value.offsetWidth
  // 获取 tagList 子元素实际宽度
  const tagItemWidth = tagListRef.value.children[0].scrollWidth
  // 判断 tagList 宽度是否小于 tagItem 实际宽度
  if (tagListWidth < tagItemWidth) {
    // 设置 tagMore 为 true
    tagMore.value = true
    // 计算向右偏移宽度
    const rightOffset = tagListRef.value.children[0].offsetWidth - tagListRef.value.clientWidth
    ;(tagListRef.value.children[0] as HTMLElement).style.right = rightOffset + 'px'
  } else {
    tagMore.value = false
  }
}

// Tag 标签上一页
const handleTagPrev = (): void => {
  if (!tagListRef.value) return

  let leftOffset = tagListRef.value.clientWidth - tagListRef.value.children[0].offsetWidth

  if (leftOffset < 0) {
    leftOffset = 0
  }
  ;(tagListRef.value.children[0] as HTMLElement).style.right = leftOffset + 'px'
}

// Tag 标签下一页
const handleTagNext = (): void => {
  if (!tagListRef.value) return

  // 计算向右偏移宽度
  const rightOffset = tagListRef.value.children[0].offsetWidth - tagListRef.value.clientWidth
  ;(tagListRef.value.children[0] as HTMLElement).style.right = rightOffset + 'px'
}

// Tag 关闭事件
const handleTagClose = (view: TagView, index: number): void => {
  // 判断是否为当前选中
  if (view.path === route.path) {
    // 判断是否为第一个
    if (index !== 0) {
      router.push(store.visitedViews[index - 1].path!)
    } else {
      router.push(store.visitedViews[index + 1].path!)
    }
  }
  store.delVisitedViews(view as RouteLocationNormalized)
}

// const handleDelTag = (v: string): void => {
//   switch (v) {
//     case 'left':
//       store.delLeftVisitedViews(route)
//       break
//     case 'right':
//       store.delRightVisitedViews(route)
//       break
//     case 'all':
//       store.delAllVisitedViews(route)
//       break
//     default:
//       console.log('参数错误')
//   }
// }

// 监听 store 数据变化
watch(store.visitedViews, () => {
  // 重新计算 tagList 宽度
  checkTagListWidth()
})

// 监听路由变化
watch(
  () => route.path,
  () => {
    store.addVisitedViews(route)
  },
  { immediate: true },
)

onMounted(() => {
  checkTagListWidth()
})
</script>

<template>
  <div class="tag-view-main">
    <div class="tag-card">
      <div class="tag-prev" v-if="tagMore" @click="handleTagPrev">
        <LeftOutlined :style="{ fontSize: '18px' }" />
      </div>

      <div class="tag-list" ref="tagListRef">
        <transition-group name="tag-fade">
          <a-tag
            v-for="(view, index) in store.visitedViews"
            :key="view.name"
            :color="view.meta?.title === route.meta?.title ? '#1890ff' : ''"
            @close="handleTagClose(view, index)"
            :closable="store.visitedViews.length > 1"
            :style="{ marginRight: '5px' }"
          >
            <router-link :to="view.path!" class="tag-link">
              {{ view.title }}
            </router-link>
          </a-tag>
        </transition-group>
      </div>

      <div class="tag-next" v-if="tagMore" @click="handleTagNext">
        <RightOutlined :style="{ fontSize: '18px' }" />
      </div>
    </div>

    <!--    <div class="tag-close">-->
    <!--      <a-dropdown @select="handleDelTag">-->
    <!--        <a-button>-->
    <!--          <template #icon>-->
    <!--            <icon-down />-->
    <!--          </template>-->
    <!--        </a-button>-->
    <!--        <template #content>-->
    <!--          <a-doption value="left">关闭左侧</a-doption>-->
    <!--          <a-doption value="right">关闭右侧</a-doption>-->
    <!--          <a-doption value="all">关闭全部</a-doption>-->
    <!--        </template>-->
    <!--      </a-dropdown>-->
    <!--    </div>-->
  </div>
</template>

<style lang="scss" scoped>
.tag-view-main {
  padding: 0 15px;
  display: flex;
  justify-content: space-between;
  max-width: 100%;
  min-width: 100%;
  background-color: var(--color-bg-2);
  .tag-card {
    display: flex;
    align-items: center;
    height: 36px;
    overflow: hidden;
    .tag-list {
      position: relative;
      overflow-x: hidden;
      transition: 0.3s ease-in-out;
    }
    .tag-prev,
    .tag-next {
      width: 48px;
      cursor: pointer;
      text-align: center;
    }
  }
}

.tag-link {
  color: inherit;
}
.tag-link:hover {
  color: inherit;
}

.tag-fade-enter-active,
.tag-fade-leave-active {
  transition: opacity 0.3s ease-in-out;
}

.tag-fade-enter-from,
.tag-fade-leave-to {
  opacity: 0;
}
</style>
