{"name": "kefu-system", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/icons-vue": "^7.0.1", "@arco-themes/vue-go-admin": "^0.0.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-json": "^6.0.2", "@codemirror/theme-one-dark": "^6.1.3", "@iconify/vue": "^5.0.0", "@remixicon/vue": "^4.6.0", "@vueuse/core": "^13.4.0", "ant-design-vue": "^4.2.6", "axios": "^1.10.0", "codemirror": "^6.0.2", "cropperjs": "^2.0.0", "js-cookie": "^3.0.5", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-codemirror": "^6.1.1", "vue-router": "^4.5.1"}, "devDependencies": {"@arco-design/web-vue": "^2.57.0", "@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.32", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.1", "@vue/tsconfig": "^0.7.0", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "mockjs": "^1.1.0", "npm-run-all2": "^8.0.4", "prettier": "3.5.3", "sass": "^1.89.2", "typescript": "~5.8.0", "unplugin-vue-components": "^28.7.0", "vite": "^7.0.0", "vite-plugin-mock": "^3.0.2", "vite-plugin-vue-devtools": "^7.7.7", "vite-svg-loader": "^5.1.0", "vue-tsc": "^2.2.10"}}