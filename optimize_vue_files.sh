#!/bin/bash

# 查找所有Vue文件
echo "查找所有Vue文件中使用getCurrentInstance的文件..."
find src -name "*.vue" -exec grep -l "getCurrentInstance" {} \; > vue_files_to_optimize.txt

# 显示找到的文件
echo "找到以下文件需要优化:"
cat vue_files_to_optimize.txt
echo "共计 $(wc -l < vue_files_to_optimize.txt) 个文件"

# 查找所有使用field属性的文件
echo -e "\n查找所有使用field属性的文件..."
find src -name "*.vue" -exec grep -l "field=" {} \; > vue_files_with_field.txt

# 显示找到的文件
echo "找到以下文件使用field属性:"
cat vue_files_with_field.txt
echo "共计 $(wc -l < vue_files_with_field.txt) 个文件"

# 查找所有使用proxy.$message、proxy.$modal或proxy.$notification的文件
echo -e "\n查找所有使用proxy.$message、proxy.$modal或proxy.$notification的文件..."
find src -name "*.vue" -exec grep -l "proxy\.\$message\|proxy\.\$modal\|proxy\.\$notification" {} \; > vue_files_with_proxy_methods.txt

# 显示找到的文件
echo "找到以下文件使用proxy方法:"
cat vue_files_with_proxy_methods.txt
echo "共计 $(wc -l < vue_files_with_proxy_methods.txt) 个文件"

# 合并所有需要优化的文件并去重
cat vue_files_to_optimize.txt vue_files_with_field.txt vue_files_with_proxy_methods.txt | sort | uniq > all_vue_files_to_optimize.txt

echo -e "\n所有需要优化的文件总计 $(wc -l < all_vue_files_to_optimize.txt) 个"

# 清理临时文件
rm vue_files_to_optimize.txt vue_files_with_field.txt vue_files_with_proxy_methods.txt

echo "优化脚本准备完成，请按照文件列表逐个优化"
